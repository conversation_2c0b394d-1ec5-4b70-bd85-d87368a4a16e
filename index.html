<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打飞机游戏</title>
    <style>
        body { margin: 0; overflow: hidden; background: #000; }
        canvas { display: block; }
        #score { position: absolute; top: 10px; left: 10px; color: white; font-size: 20px; }
    </style>
</head>
<body>
    <div id="score">分数: 0</div>
    <canvas id="gameCanvas"></canvas>
    <script>
        // 游戏初始化
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        
        // 设置画布大小
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // 游戏变量
        let score = 0;
        let gameOver = false;
        
        // 玩家飞机
        const player = {
            x: canvas.width / 2,
            y: canvas.height - 50,
            width: 50,
            height: 50,
            speed: 8
        };
        
        // 子弹数组
        const bullets = [];
        
        // 敌机数组
        const enemies = [];
        
        // 按键状态
        const keys = {};
        
        // 监听键盘事件
        window.addEventListener('keydown', (e) => { keys[e.key] = true; });
        window.addEventListener('keyup', (e) => { keys[e.key] = false; });
        
        // 创建子弹
        function createBullet() {
            bullets.push({
                x: player.x + player.width / 2 - 2,
                y: player.y,
                width: 4,
                height: 10,
                speed: 10
            });
        }
        
        // 创建敌机
        function createEnemy() {
            const size = Math.random() * 30 + 20;
            enemies.push({
                x: Math.random() * (canvas.width - size),
                y: -size,
                width: size,
                height: size,
                speed: Math.random() * 2 + 1
            });
        }
        
        // 检测碰撞
        function checkCollision(rect1, rect2) {
            return rect1.x < rect2.x + rect2.width &&
                   rect1.x + rect1.width > rect2.x &&
                   rect1.y < rect2.y + rect2.height &&
                   rect1.y + rect1.height > rect2.y;
        }
        
        // 游戏主循环
        let lastBulletTime = 0;
        let lastEnemyTime = 0;
        
        function gameLoop(timestamp) {
            if (gameOver) return;
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 移动玩家
            if (keys['ArrowLeft'] && player.x > 0) player.x -= player.speed;
            if (keys['ArrowRight'] && player.x < canvas.width - player.width) player.x += player.speed;
            
            // 自动发射子弹
            if (timestamp - lastBulletTime > 200) {
                createBullet();
                lastBulletTime = timestamp;
            }
            
            // 创建敌机
            if (timestamp - lastEnemyTime > 1000) {
                createEnemy();
                lastEnemyTime = timestamp;
            }
            
            // 绘制和更新子弹
            ctx.fillStyle = 'white';
            bullets.forEach((bullet, bulletIndex) => {
                bullet.y -= bullet.speed;
                ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
                
                // 移除超出屏幕的子弹
                if (bullet.y < 0) bullets.splice(bulletIndex, 1);
                
                // 检测子弹与敌机碰撞
                enemies.forEach((enemy, enemyIndex) => {
                    if (checkCollision(bullet, enemy)) {
                        bullets.splice(bulletIndex, 1);
                        enemies.splice(enemyIndex, 1);
                        score += 10;
                        scoreElement.textContent = `分数: ${score}`;
                    }
                });
            });
            
            // 绘制和更新敌机
            ctx.fillStyle = 'red';
            enemies.forEach((enemy, index) => {
                enemy.y += enemy.speed;
                ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
                
                // 移除超出屏幕的敌机
                if (enemy.y > canvas.height) enemies.splice(index, 1);
                
                // 检测敌机与玩家碰撞
                if (checkCollision(enemy, player)) {
                    gameOver = true;
                    alert(`游戏结束! 你的分数是: ${score}`);
                }
            });
            
            // 绘制玩家
            ctx.fillStyle = 'blue';
            ctx.fillRect(player.x, player.y, player.width, player.height);
            
            requestAnimationFrame(gameLoop);
        }
        
        // 开始游戏
        requestAnimationFrame(gameLoop);
    </script>
</body>
</html>